#pragma once
#include <Arduino.h>

// 检查ESP-IDF版本，使用相应的ADC驱动
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.x 使用新的ADC驱动
    #include "esp_adc/adc_oneshot.h"
    #include "esp_adc/adc_cali.h"
    #include "esp_adc/adc_cali_scheme.h"
    #define USE_NEW_ADC_DRIVER 1
#else
    // ESP-IDF 4.x 使用旧的ADC驱动
    #include "esp_adc_cal.h"
    #include "driver/adc.h"
    #define USE_NEW_ADC_DRIVER 0
#endif

typedef struct
{
    float voltage_divider_factor; // 电压分压系数
    uint8_t adc_pin;
    adc_unit_t adc_unit;
    adc_channel_t adc_channel;       // 统一使用adc_channel_t
    adc_atten_t adc_atten;
    const uint16_t default_vref;

#if USE_NEW_ADC_DRIVER
    // 新ADC驱动 (ESP-IDF 5.x)
    adc_bitwidth_t adc_width;
    adc_oneshot_unit_handle_t adc_handle;
    adc_cali_handle_t cali_handle;
    bool calibrated;
#else
    // 旧ADC驱动 (ESP-IDF 4.x)
    adc_bits_width_t adc_width;
    // 连续采样相关字段
    uint16_t adc1_chan_mask;
    uint16_t adc2_chan_mask;
    adc_channel_t channel[1];

    esp_adc_cal_characteristics_t adc_chars;
    esp_adc_cal_value_t cal_type;
#endif
} adc_config_t;

#ifdef __cplusplus
extern "C" {
#endif
void adc_calibration(adc_config_t *adc_config);
void adc_single_init(adc_config_t *adc_config);
void adc_continuous_init(adc_config_t *adc_config);
float ReadVoltageContinuous(adc_config_t *adc_config);
float ReadVoltageSingle(adc_config_t *adc_config);
#ifdef __cplusplus
}
#endif