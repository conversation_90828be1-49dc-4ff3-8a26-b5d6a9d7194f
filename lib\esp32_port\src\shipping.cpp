#include <Preferences.h>
#include <OneButton.h>
#include "esp_sleep.h"
#include "driver/rtc_io.h"
#include "driver/gpio.h"
#include "tuya_config.h"
#include "shipping.h"
#include "tuya_log.h"

// 按钮GPIO定义
#define BUTTON_GPIO6    GPIO_NUM_6
#define BUTTON_GPIO7    GPIO_NUM_7

// 全局变量
static OneButton button6(BUTTON_GPIO6, true, true);  // GPIO6, active LOW, enable pullup
static OneButton button7(BUTTON_GPIO7, true, true);  // GPIO7, active LOW, enable pullup
static volatile bool wakeup_requested = false;

Preferences prefs;

// 按钮事件回调函数
void button6_click() {
    TY_LOGI("Button GPIO6 clicked");
}

void button6_longpress_start() {
    TY_LOGI("Button GPIO6 long press started (3s) - requesting wakeup");
    wakeup_requested = true;
}

void button6_longpress_stop() {
    TY_LOGI("Button GPIO6 long press stopped");
}

void button7_click() {
    TY_LOGI("Button GPIO7 clicked");
}

void button7_longpress_start() {
    TY_LOGI("Button GPIO7 long press started (3s) - requesting wakeup");
    wakeup_requested = true;
}

void button7_longpress_stop() {
    TY_LOGI("Button GPIO7 long press stopped");
}

// 配置单个按钮的所有参数
void configure_button(OneButton &button, void (*click_func)(), void (*longpress_start_func)(), void (*longpress_stop_func)()) {
    button.attachClick(click_func);
    button.attachLongPressStart(longpress_start_func);
    button.attachLongPressStop(longpress_stop_func);
    button.setPressMs(3000);              // 3秒长按
    button.setClickMs(50);                // 点击检测时间
    button.setDebounceMs(50);             // 去抖时间
}

// 初始化按钮
void init_wakeup_buttons() {
    TY_LOGI("Initializing wakeup buttons on GPIO6 and GPIO7");

    // 配置按钮6和按钮7
    configure_button(button6, button6_click, button6_longpress_start, button6_longpress_stop);
    configure_button(button7, button7_click, button7_longpress_start, button7_longpress_stop);

    wakeup_requested = false;
}

// 检查按钮状态（使用OneButton库）
bool check_wakeup_buttons() {
    button6.tick();
    button7.tick();
    return wakeup_requested;
}

// 获取按钮按下状态（使用OneButton库）
bool is_button6_pressed() {
    button6.tick();
    // OneButton库中检查按钮状态的方法
    return (digitalRead(BUTTON_GPIO6) == LOW);  // 按钮按下时为低电平
}

bool is_button7_pressed() {
    button7.tick();
    // OneButton库中检查按钮状态的方法
    return (digitalRead(BUTTON_GPIO7) == LOW);  // 按钮按下时为低电平
}

// 这些函数已被删除，因为OneButton库已经处理了所有的长按检测：
// - wait_button_release(): 不再需要等待按钮释放
// - check_long_press_wakeup_nonblocking(): OneButton库自动处理时间检测

// 立即检查是否已经有长按事件（用于已经触发的情况）
bool is_long_press_triggered() {
    button6.tick();
    button7.tick();
    return wakeup_requested;
}

// 重置唤醒请求状态和按钮状态
void reset_wakeup_request() {
    TY_LOGI("Resetting wakeup request and button states");
    wakeup_requested = false;

    // 彻底重置OneButton库的内部状态
    // 通过重新创建按钮对象来清除所有内部状态
    button6 = OneButton(BUTTON_GPIO6, true, true);
    button7 = OneButton(BUTTON_GPIO7, true, true);

    // 重新配置按钮（使用统一的配置函数）
    configure_button(button6, button6_click, button6_longpress_start, button6_longpress_stop);
    configure_button(button7, button7_click, button7_longpress_start, button7_longpress_stop);
}

bool should_enter_shipping_mode()
{
    prefs.begin(TUYA_NAMESPACE, true);
    size_t tuya_activated = prefs.getBytesLength(TUYA_NAMESPACE);
    prefs.end();
    TY_LOGI("tuya_activated info size: %d", tuya_activated);
    return tuya_activated==0;
}

void clear_wifi_info()
{
    prefs.begin("wifi_creds", false);
    size_t networks_count = prefs.getUInt("count", 0);
    TY_LOGI("Loading %d saved networks\n", networks_count);

    for (size_t i = 0; i < networks_count; i++)
    {
        String prefix = "net" + String(i);
        String ssid_key = prefix + "_ssid";
        String pass_key = prefix + "_pass";
        String ssid = prefs.getString(ssid_key.c_str(), "");
        String password = prefs.getString(pass_key.c_str(), "");
        TY_LOGI("Removed WIFI credentials for SSID: %s, password: %s\n", ssid.c_str(), password.c_str());
        prefs.remove(ssid_key.c_str());
        prefs.remove(pass_key.c_str());
    }
    prefs.putUInt("count", 0);
    prefs.end();
}

void factory_reset()
{
    clear_wifi_info();
    clear_tuya_activated_info(); 
}

void clear_tuya_activated_info()
{
    prefs.begin(TUYA_NAMESPACE, false);
    prefs.remove(TUYA_NAMESPACE);
    prefs.end();
    TY_LOGI("Cleared Tuya activated info");
}

void light_sleep()
{
    TY_LOGI("Entering light sleep mode...");
    TY_LOGI("Press and hold GPIO6 or GPIO7 for 3 seconds to wake up");

    // 初始化按钮
    init_wakeup_buttons();

    // 配置GPIO6和GPIO7为唤醒源
    gpio_set_direction(BUTTON_GPIO6, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO6, GPIO_PULLUP_ONLY);
    gpio_wakeup_enable(BUTTON_GPIO6, GPIO_INTR_LOW_LEVEL);  // 按钮按下时高低电平

    gpio_set_direction(BUTTON_GPIO7, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO7, GPIO_PULLUP_ONLY);
    gpio_wakeup_enable(BUTTON_GPIO7, GPIO_INTR_LOW_LEVEL);  // 按钮按下时为低电平

    // 启用GPIO唤醒
    esp_sleep_enable_gpio_wakeup();

    // 进入轻度睡眠
    esp_light_sleep_start();

    // 唤醒后的处理
    esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();
    switch(wakeup_reason) {
        case ESP_SLEEP_WAKEUP_GPIO:
            TY_LOGI("Wakeup caused by GPIO");

            // 重新初始化按钮（唤醒后可能需要重新初始化）
            init_wakeup_buttons();

            // 检查是否为长按唤醒（需要给OneButton库时间处理）
            if (is_button6_pressed() || is_button7_pressed()) {
                TY_LOGI("Button is pressed after wakeup, checking for long press...");

                // 确保从干净的状态开始检测
                wakeup_requested = false;

                // 给OneButton库时间来检测长按（最多等待3.5秒）
                unsigned long check_start = millis();
                while ((is_button6_pressed() || is_button7_pressed()) &&
                       !wakeup_requested &&
                       (millis() - check_start < 3500)) {
                    button6.tick();  // 让OneButton库处理时间和事件
                    button7.tick();
                    // 优化：增加延迟时间，减少CPU使用率
                    vTaskDelay(pdMS_TO_TICKS(50)); // 从10ms增加到50ms
                }

                if (wakeup_requested) {
                    TY_LOGI("Long press confirmed - exiting shipping mode immediately");
                    return;  // 立即退出，无需等待按钮释放
                } else {
                    TY_LOGI("Short press or timeout - returning to sleep");
                    reset_wakeup_request();
                    light_sleep();  // 重新进入睡眠
                    return;
                }
            } else {
                TY_LOGI("Button not pressed after wakeup - returning to sleep");
                reset_wakeup_request();
                light_sleep();  // 重新进入睡眠
                return;
            }
            break;
        default:
            TY_LOGI("Wakeup was not caused by GPIO");
            break;
    }
}

void deep_sleep()
{
    TY_LOGI("Entering deep sleep mode...");
    TY_LOGI("Press and hold GPIO6 or GPIO7 for 3 seconds to wake up");

    // 初始化按钮（确保GPIO配置正确）
    init_wakeup_buttons();

    // 禁用GPIO深度睡眠保持
    gpio_deep_sleep_hold_dis();

    // 配置GPIO6和GPIO7为唤醒源
    gpio_set_direction(BUTTON_GPIO6, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO6, GPIO_PULLUP_ONLY);

    gpio_set_direction(BUTTON_GPIO7, GPIO_MODE_INPUT);
    gpio_set_pull_mode(BUTTON_GPIO7, GPIO_PULLUP_ONLY);

    // 设置GPIO唤醒位掩码 (GPIO6 = bit 6, GPIO7 = bit 7)
    uint64_t wakeup_pin_mask = (1ULL << BUTTON_GPIO6) | (1ULL << BUTTON_GPIO7);

    // 启用GPIO深度睡眠唤醒（低电平触发，因为按钮按下时为低电平）
    esp_deep_sleep_enable_gpio_wakeup(wakeup_pin_mask, ESP_GPIO_WAKEUP_GPIO_LOW);

    // 进入深度睡眠
    esp_deep_sleep_start();
}

// 深度睡眠唤醒后的按钮检查（在main.cpp的setup中调用）
bool check_deep_sleep_wakeup() {
    TY_LOGI("Checking deep sleep wakeup with OneButton...");

    // 初始化按钮
    init_wakeup_buttons();

    // 短暂延迟让系统稳定
    vTaskDelay(pdMS_TO_TICKS(100));

    // 检查是否有按钮被按下
    if (is_button6_pressed() || is_button7_pressed()) {
        TY_LOGI("Button still pressed after deep sleep wakeup");

        // 检查是否为长按唤醒（需要给OneButton库时间处理）
        TY_LOGI("Button still pressed after deep sleep, checking for long press...");

        // 确保从干净的状态开始检测
        wakeup_requested = false;

        // 给OneButton库时间来检测长按（最多等待3.5秒）
        unsigned long check_start = millis();
        while ((is_button6_pressed() || is_button7_pressed()) &&
               !wakeup_requested &&
               (millis() - check_start < 3500)) {
            button6.tick();  // 让OneButton库处理时间和事件
            button7.tick();
            // 优化：增加延迟时间，减少CPU使用率
            vTaskDelay(pdMS_TO_TICKS(50)); // 从10ms增加到50ms
        }

        if (wakeup_requested) {
            TY_LOGI("Long press confirmed after deep sleep - clearing shipping mode immediately");
            return true;  // 确认长按，立即退出shipping模式
        } else {
            TY_LOGI("Short press or timeout after deep sleep - should return to sleep");
            return false;  // 短按，应该重新进入睡眠
        }
    } else {
        TY_LOGI("No button pressed after deep sleep wakeup - clearing shipping mode");
        return true;  // 没有按钮按下，正常退出shipping模式
    }
}
