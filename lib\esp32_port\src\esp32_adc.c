/*
 * 适配esp32c3 S3 H2芯片ADC，其它系列可能需要微调
 * 参考文档：
 * https://docs.espressif.com/projects/esp-idf/en/latest/esp32c3/api-reference/peripherals/adc.html
 * https://espressif-docs.readthedocs-hosted.com/projects/arduino-esp32/en/latest/api/adc.html
 * https://github.com/espressif/esp-idf/blob/v4.4/examples/peripherals/adc/dma_read/main/adc_dma_example_main.c
 */
#include "esp32_adc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// 单次采样
#define NO_OF_SAMPLES 8 // 多次采样求平均，采样次数，用于平均，降低噪声
// 连续采样
#define TIMES 256
#define GET_UNIT(x) ((x >> 3) & 0x1)
#if CONFIG_IDF_TARGET_ESP32C3 || CONFIG_IDF_TARGET_ESP32H2
#define ADC_RESULT_BYTE 4
#define ADC_CONV_LIMIT_EN 0
#define ADC_CONV_MODE ADC_CONV_ALTER_UNIT // ESP32C3 only supports alter mode
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#elif CONFIG_IDF_TARGET_ESP32S3
#define ADC_RESULT_BYTE 4
#define ADC_CONV_LIMIT_EN 0
#define ADC_CONV_MODE ADC_CONV_BOTH_UNIT
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#endif

void adc_calibration(adc_config_t *adc_config)
{
#if USE_NEW_ADC_DRIVER
    // 新ADC驱动校准 (ESP-IDF 5.x)
    adc_cali_curve_fitting_config_t cali_config = {
        .unit_id = adc_config->adc_unit,
        .atten = adc_config->adc_atten,
        .bitwidth = adc_config->adc_width,
    };

    esp_err_t ret = adc_cali_create_scheme_curve_fitting(&cali_config, &adc_config->cali_handle);
    if (ret == ESP_OK) {
        adc_config->calibrated = true;
        printf("ADC: Calibration scheme created successfully\n");
    } else {
        adc_config->calibrated = false;
        printf("ADC: Calibration scheme creation failed, using default Vref = %dmV\n", adc_config->default_vref);
    }
#else
    // 旧ADC驱动校准 (ESP-IDF 4.x)
    adc_config->cal_type = esp_adc_cal_characterize(adc_config->adc_unit, adc_config->adc_atten, adc_config->adc_width, adc_config->default_vref, &adc_config->adc_chars);
    if (adc_config->cal_type == ESP_ADC_CAL_VAL_EFUSE_TP)
    {
        printf("ADC: Two Point values stored in eFuse\n");
    }
    else if (adc_config->cal_type == ESP_ADC_CAL_VAL_EFUSE_TP_FIT)
    {
        printf("ADC: Two Point values and fitting curve coefficients stored in eFuse\n");
    }
    else if (adc_config->cal_type == ESP_ADC_CAL_VAL_EFUSE_VREF)
    {
        printf("ADC: reference voltage stored in eFuse Vref = %dmV\n", adc_config->adc_chars.vref);
    }
    else
    {
        printf("ADC: Default Vref = %dmV\n", adc_config->default_vref);
    }
#endif
}

void adc_single_init(adc_config_t *adc_config)
{
#if USE_NEW_ADC_DRIVER
    // 新ADC驱动初始化 (ESP-IDF 5.x)
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = adc_config->adc_unit,
    };

    esp_err_t ret = adc_oneshot_new_unit(&init_config, &adc_config->adc_handle);
    if (ret != ESP_OK) {
        printf("ADC: Failed to create ADC unit: %d\n", ret);
        return;
    }

    adc_oneshot_chan_cfg_t config = {
        .bitwidth = adc_config->adc_width,
        .atten = adc_config->adc_atten,
    };

    ret = adc_oneshot_config_channel(adc_config->adc_handle, adc_config->adc_channel, &config);
    if (ret != ESP_OK) {
        printf("ADC: Failed to configure channel: %d\n", ret);
    } else {
        printf("ADC: Channel configured successfully\n");
    }
#else
    // 旧ADC驱动初始化 (ESP-IDF 4.x)
    adc1_config_width(adc_config->adc_width);
    adc1_config_channel_atten(adc_config->adc_channel, adc_config->adc_atten);
#endif
}

void adc_continuous_init(adc_config_t *adc_config)
{
    // 连续采样功能暂时禁用，因为新旧ADC驱动API差异较大
    // 对于温度控制应用，单次采样已经足够
    printf("ADC: Continuous sampling not implemented for new ADC driver\n");
    printf("ADC: Using single shot sampling instead\n");
}

// check_valid_data函数已移除，因为不再使用连续采样

float ReadVoltageContinuous(adc_config_t *adc_config)
{
    // 连续采样功能暂时禁用，使用单次采样代替
    printf("ADC: ReadVoltageContinuous not implemented, using ReadVoltageSingle\n");
    return ReadVoltageSingle(adc_config);
}

float ReadVoltageSingle(adc_config_t *adc_config)
{
    uint32_t voltage = 0;
    int adc_reading = 0;

#if USE_NEW_ADC_DRIVER
    // 新ADC驱动 (ESP-IDF 5.x)
    if (adc_config->adc_handle == NULL) {
        printf("ADC: Handle not initialized, using Arduino analogRead\n");
        return (analogRead(adc_config->adc_pin) / 4095.0) * 3.3 * adc_config->voltage_divider_factor;
    }

    // 多次采样求平均
    uint32_t total_reading = 0;
    for (int i = 0; i < NO_OF_SAMPLES; i++) {
        int raw_reading = 0;
        esp_err_t ret = adc_oneshot_read(adc_config->adc_handle, adc_config->adc_channel, &raw_reading);
        if (ret == ESP_OK) {
            total_reading += raw_reading;
        } else {
            printf("ADC: Read failed: %d\n", ret);
        }
        vTaskDelay(pdMS_TO_TICKS(5));
    }
    adc_reading = total_reading / NO_OF_SAMPLES;

    // 校准转换
    if (adc_config->calibrated) {
        esp_err_t ret = adc_cali_raw_to_voltage(adc_config->cali_handle, adc_reading, &voltage);
        if (ret == ESP_OK) {
            return (voltage * adc_config->voltage_divider_factor / 1000.0);
        }
    }

    // 校准失败，使用默认计算
    return (adc_reading / 4095.0) * 3.3 * adc_config->voltage_divider_factor;

#else
    // 旧ADC驱动 (ESP-IDF 4.x)
    if (adc_config->cal_type != ESP_ADC_CAL_VAL_DEFAULT_VREF)
    {
        for (int i = 0; i < NO_OF_SAMPLES; i++)
        {
            adc_reading += adc1_get_raw(adc_config->adc_channel);
            vTaskDelay(pdMS_TO_TICKS(5));
        }
        adc_reading /= NO_OF_SAMPLES;
        voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_config->adc_chars);
        return (voltage * adc_config->voltage_divider_factor / 1000.0);
    }
    else
    {
        return (analogRead(adc_config->adc_pin) / 4095.0) * 3.3 * adc_config->voltage_divider_factor * (1100 / adc_config->default_vref);
    }
#endif
}
